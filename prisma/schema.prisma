generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                     String                @id @default(cuid())
  name                   String?
  username               String?               @unique
  email                  String?               @unique
  emailVerified          DateTime?
  image                  String?
  password               String?
  provider               String?               @default("credentials")
  role                   Role                  @default(OWNER)
  token                  String?               @unique
  phone                  String?
  bio                    String?
  birthday               DateTime?
  lastLogin              DateTime?
  hasCompletedOnboarding Boolean               @default(false)
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt
  currentPlan            SubscriptionPlan      @default(BASIC)
  subscriptionExpiry     DateTime?
  trialStartDate         DateTime?
  trialEndDate           DateTime?
  isTrialActive          Boolean               @default(false)
  accounts               Account[]
  categories             Category[]
  customers              Customer[]
  employees              Employee[]
  notificationSettings   NotificationSettings?
  notifications          Notification[]
  payments               Payment[]
  products               Product[]
  purchases              Purchase[]
  sales                  Sale[]
  services               Service[]
  sessions               Session[]
  subscriptions          Subscription[]
  suppliers              Supplier[]
  units                  Unit[]
  warehouses             Warehouse[]
  stockMovements         StockMovement[]
  additionalInfo         AdditionalUserInfo?
  EventDiscount          EventDiscount[]
  OrderCounter           OrderCounter[]

  @@map("users")
}

model AdditionalUserInfo {
  id                  String   @id @default(cuid())
  userId              String   @unique
  companyId           String?  @unique
  companyName         String?
  companyUsername     String?  @unique
  companyPhone        String?
  companyEmail        String? // email
  companyAddress      String? // alamat perusahaan
  companyFaxNumber    String? // nomor fax
  companyLogo         String? // logo perusahaan
  companyWebsite      String? // website
  position            String?
  employeeCount       String?
  occupation          String?
  industry            String?
  subscriptionPackage String?
  referralCode        String?
  postalCode          String? // kode pos
  city                String? // kota
  billingAddress      String? // alamat penagihan (legacy - kept for backward compatibility)
  shippingAddress     String? // alamat pengiriman (legacy - kept for backward compatibility)
  billingAddresses    String[] @default([]) // multiple billing addresses
  shippingAddresses   String[] @default([]) // multiple shipping addresses
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("additional_user_info")
}

model Employee {
  id            String   @id @default(cuid())
  name          String
  employeeId    String
  password      String
  role          Role     @default(CASHIER)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  ownerId       String
  ownerUsername String
  owner         User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  sales         Sale[]

  @@unique([ownerId, employeeId])
  @@index([ownerId])
  @@map("employees")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("sessions")
}

model VerificationToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())

  @@unique([email, token])
  @@map("verification_tokens")
}

model Product {
  id                    String                 @id
  name                  String
  description           String?
  sku                   String?
  barcode               String?
  price                 Decimal                @db.Decimal(10, 2)
  cost                  Decimal?               @db.Decimal(10, 2)
  stock                 Int                    @default(0)
  image                 String?
  weight                Int?                   @default(0)
  length                Decimal?               @db.Decimal(10, 2)
  width                 Decimal?               @db.Decimal(10, 2)
  height                Decimal?               @db.Decimal(10, 2)
  unit                  String                 @default("Pcs")
  unitId                String?
  tags                  String[]
  isDraft               Boolean                @default(false)
  hasVariants           Boolean                @default(false)
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  userId                String
  categoryId            String?
  costPriceTaxRate      Decimal?               @default(0) @db.Decimal(5, 2)
  salePriceTaxRate      Decimal?               @default(0) @db.Decimal(5, 2)
  wholesalePrice        Decimal?               @db.Decimal(10, 2)
  wholesalePriceTaxRate Decimal?               @default(0) @db.Decimal(5, 2)
  variants              ProductVariant[]
  category              Category?              @relation(fields: [categoryId], references: [id])
  unitModel             Unit?                  @relation(fields: [unitId], references: [id])
  user                  User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  purchaseItems         PurchaseItem[]
  saleItems             SaleItem[]
  warehouseStocks       WarehouseStock[]
  stockMovements        StockMovement[]
  EventDiscountProduct  EventDiscountProduct[]

  @@unique([userId, sku])
  @@index([userId])
  @@index([categoryId])
  @@map("products")
}

model EventDiscount {
  id                 String                 @id @default(cuid())
  name               String
  description        String?
  discountPercentage Decimal                @db.Decimal(5, 2)
  startDate          DateTime
  endDate            DateTime
  isActive           Boolean                @default(true)
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  userId             String
  user               User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  products           EventDiscountProduct[]
  sales              Sale[]

  @@index([userId])
  @@index([startDate, endDate])
  @@map("event_discounts")
}

model EventDiscountProduct {
  id              String        @id @default(cuid())
  eventDiscountId String
  productId       String
  createdAt       DateTime      @default(now())
  eventDiscount   EventDiscount @relation(fields: [eventDiscountId], references: [id], onDelete: Cascade)
  product         Product       @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([eventDiscountId, productId])
  @@index([eventDiscountId])
  @@index([productId])
  @@map("event_discount_products")
}

model Category {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  userId    String
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  products  Product[]

  @@unique([userId, name])
  @@index([userId])
  @@map("categories")
}

model Unit {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  userId    String
  products  Product[]
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name])
  @@index([userId])
  @@map("units")
}

model ProductVariant {
  id        String   @id @default(cuid())
  sku       String?
  colorName String
  colorCode String
  price     Decimal? @db.Decimal(10, 2)
  stock     Int      @default(0)
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  productId String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@map("product_variants")
}

model Sale {
  id                String         @id
  saleDate          DateTime       @default(now())
  totalAmount       Decimal        @db.Decimal(12, 2)
  transactionNumber String?
  invoiceRef        String?
  isDraft           Boolean        @default(false)
  isPublic          Boolean        @default(false)
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  customerId        String?
  customerRefNumber String?
  shippingAddress   String?
  paymentDueDate    DateTime?
  paymentTerms      String?
  warehouseId       String?
  tags              String[]
  memo              String?
  lampiran          Json[]
  priceIncludesTax  Boolean        @default(false)
  userId            String
  employeeId        String?
  items             SaleItem[]
  customer          Customer?      @relation(fields: [customerId], references: [id])
  Employee          Employee?      @relation(fields: [employeeId], references: [id])
  user              User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse         Warehouse?     @relation(fields: [warehouseId], references: [id])
  EventDiscount     EventDiscount? @relation(fields: [eventDiscountId], references: [id])
  eventDiscountId   String?

  @@index([userId])
  @@index([customerId])
  @@index([warehouseId])
  @@map("sales")
}

model SaleItem {
  id                 String   @id @default(cuid())
  quantity           Int
  priceAtSale        Decimal  @db.Decimal(10, 2)
  discountPercentage Decimal? @db.Decimal(5, 2) // Store discount percentage (0-100)
  discountAmount     Decimal? @db.Decimal(10, 2) // Store actual discount amount
  eventDiscountId    String? // Reference to the event discount that was applied
  eventDiscountName  String? // Store the name of the event discount for historical reference
  isWholesale        Boolean  @default(false) // Track if wholesale price was used
  unit               String?  @default("Buah") // Store unit information
  tax                String? // Store tax information
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  saleId             String
  productId          String
  product            Product  @relation(fields: [productId], references: [id])
  sale               Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@index([saleId])
  @@index([productId])
  @@index([eventDiscountId])
  @@map("sale_items")
}

model Purchase {
  id                String         @id
  purchaseDate      DateTime       @default(now())
  totalAmount       Decimal        @db.Decimal(12, 2)
  invoiceRef        String?
  isDraft           Boolean        @default(false)
  isPublic          Boolean        @default(false)
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  supplierEmail     String?
  transactionDate   DateTime       @default(now())
  paymentDueDate    DateTime?
  transactionNumber String?
  tags              String[]
  billingAddress    String?
  memo              String?
  lampiran          Json[]
  warehouseId       String?
  userId            String
  supplierId        String?
  items             PurchaseItem[]
  supplier          Supplier?      @relation(fields: [supplierId], references: [id])
  user              User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse         Warehouse?     @relation(fields: [warehouseId], references: [id])

  @@index([userId])
  @@index([supplierId])
  @@index([warehouseId])
  @@map("purchases")
}

model PurchaseItem {
  id                 String   @id @default(cuid())
  quantity           Int
  costAtPurchase     Decimal  @db.Decimal(10, 2)
  discountPercentage Decimal? @db.Decimal(5, 2) // Store discount percentage (0-100)
  discountAmount     Decimal? @db.Decimal(10, 2) // Store actual discount amount
  description        String?
  unit               String?  @default("Buah")
  tax                String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  purchaseId         String
  productId          String
  product            Product  @relation(fields: [productId], references: [id])
  purchase           Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@index([purchaseId])
  @@index([productId])
  @@map("purchase_items")
}

model Supplier {
  id              String                @id
  name            String
  firstName       String?
  middleName      String?
  lastName        String?
  contactName     String?
  phone           String?
  telephone       String?
  fax             String?
  email           String?
  identityType    String?
  identityNumber  String?
  NIK             String?
  NPWP            String?
  companyName     String?
  otherInfo       String?
  address         String?
  billingAddress  String?
  shippingAddress String?
  sameAsShipping  Boolean               @default(false)
  bankName        String?
  bankBranch      String?
  accountHolder   String?
  accountNumber   String?
  notes           String?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  userId          String
  purchases       Purchase[]
  bankAccounts    SupplierBankAccount[]
  user            User                  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, email])
  @@index([userId])
  @@map("suppliers")
}

model Customer {
  id              String                @id
  name            String
  firstName       String?
  middleName      String?
  lastName        String?
  contactName     String?
  phone           String?
  telephone       String?
  fax             String?
  email           String?
  identityType    String?
  identityNumber  String?
  NIK             String?
  NPWP            String?
  companyName     String?
  otherInfo       String?
  address         String?
  billingAddress  String?
  shippingAddress String?
  sameAsShipping  Boolean               @default(false)
  bankName        String?
  bankBranch      String?
  accountHolder   String?
  accountNumber   String?
  notes           String?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  userId          String
  bankAccounts    CustomerBankAccount[]
  user            User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  sales           Sale[]

  @@unique([userId, email])
  @@index([userId])
  @@map("customers")
}

model CustomerBankAccount {
  id            String   @id @default(cuid())
  bankName      String
  bankBranch    String?
  accountHolder String
  accountNumber String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  customerId    String
  customer      Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
  @@map("customer_bank_accounts")
}

model SupplierBankAccount {
  id            String   @id @default(cuid())
  bankName      String
  bankBranch    String?
  accountHolder String
  accountNumber String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  supplierId    String
  supplier      Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

  @@index([supplierId])
  @@map("supplier_bank_accounts")
}

model Notification {
  id        String           @id @default(cuid())
  type      NotificationType @default(INFO)
  title     String
  message   String
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("notifications")
}

model NotificationSettings {
  id                          String   @id @default(cuid())
  emailEnabled                Boolean  @default(true)
  emailInfoEnabled            Boolean  @default(true)
  emailWarningEnabled         Boolean  @default(true)
  emailSuccessEnabled         Boolean  @default(true)
  emailErrorEnabled           Boolean  @default(true)
  emailPurchaseEnabled        Boolean  @default(true)
  emailProductAdditionEnabled Boolean  @default(true)
  emailServiceAdditionEnabled Boolean  @default(true)
  dailySummary                Boolean  @default(false)
  weeklySummary               Boolean  @default(true)
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
  userId                      String   @unique
  user                        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}

model Subscription {
  id        String           @id @default(cuid())
  plan      SubscriptionPlan
  startDate DateTime         @default(now())
  endDate   DateTime
  autoRenew Boolean          @default(false)
  status    String           @default("active")
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  userId    String
  payments  Payment[]
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("subscriptions")
}

model Payment {
  id             String        @id @default(cuid())
  amount         Decimal       @db.Decimal(12, 2)
  currency       String        @default("IDR")
  status         PaymentStatus @default(PENDING)
  paymentMethod  String?
  externalId     String?
  externalUrl    String?
  invoiceId      String?
  paymentDate    DateTime?
  expiryDate     DateTime?
  metadata       Json?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String
  subscriptionId String?
  serviceId      String?
  service        Service?      @relation(fields: [serviceId], references: [id])
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([subscriptionId])
  @@index([serviceId])
  @@map("payments")
}

model OrderCounter {
  id        String   @id @default(cuid())
  userId    String
  companyId String?
  planName  String
  date      String // Format: YYYYMMDD
  counter   Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, companyId, planName, date])
  @@index([userId])
  @@index([companyId])
  @@map("order_counters")
}

model Service {
  id                      String                 @id @default(cuid())
  serviceNumber           String
  customerName            String
  customerPhone           String
  customerEmail           String?
  deviceType              DeviceType             @default(OTHER)
  deviceBrand             String
  deviceModel             String
  deviceSerialNumber      String?
  problemDescription      String
  diagnosisNotes          String?
  repairNotes             String?
  estimatedCost           Decimal?               @db.Decimal(12, 2)
  finalCost               Decimal?               @db.Decimal(12, 2)
  warrantyPeriod          Int?                   @default(0)
  lampiran                Json[]
  status                  ServiceStatus          @default(DITERIMA)
  receivedDate            DateTime               @default(now())
  estimatedCompletionDate DateTime?
  completedDate           DateTime?
  deliveredDate           DateTime?
  isDraft                 Boolean                @default(false)
  createdAt               DateTime               @default(now())
  updatedAt               DateTime               @updatedAt
  userId                  String
  customerId              String?
  payments                Payment[]
  serviceHistory          ServiceStatusHistory[]
  user                    User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("services")
}

model ServiceStatusHistory {
  id        String        @id @default(cuid())
  status    ServiceStatus
  notes     String?
  changedAt DateTime      @default(now())
  changedBy String
  serviceId String
  service   Service       @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@index([serviceId])
  @@map("service_status_history")
}

model Warehouse {
  id          String   @id @default(cuid())
  name        String
  description String?
  address     String?
  phone       String?
  email       String?
  contactName String?
  isActive    Boolean  @default(true)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relations
  stocks         WarehouseStock[]
  stockMovements StockMovement[]
  sales          Sale[]
  purchases      Purchase[]

  @@unique([userId, name])
  @@index([userId])
  @@map("warehouses")
}

model WarehouseStock {
  id          String    @id @default(cuid())
  quantity    Int       @default(0)
  minLevel    Int?      @default(0)
  maxLevel    Int?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  productId   String
  warehouseId String
  product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([productId, warehouseId])
  @@index([productId])
  @@index([warehouseId])
  @@map("warehouse_stocks")
}

model StockMovement {
  id            String            @id @default(cuid())
  type          StockMovementType
  quantity      Int
  previousStock Int
  newStock      Int
  reference     String? // Reference to sale/purchase/transfer ID
  notes         String?
  createdAt     DateTime          @default(now())
  productId     String
  warehouseId   String
  userId        String
  product       Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
  warehouse     Warehouse         @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  user          User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([warehouseId])
  @@index([userId])
  @@index([createdAt])
  @@map("stock_movements")
}

enum Role {
  OWNER
  ADMIN
  CASHIER
}

enum SubscriptionPlan {
  BASIC
  PRO
  ENTERPRISE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  EXPIRED
  REFUNDED
}

enum NotificationType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

enum ServiceStatus {
  DITERIMA
  PROSES_MENUNGGU_SPAREPART
  SELESAI_BELUM_DIAMBIL
  SELESAI_SUDAH_DIAMBIL
}

enum DeviceType {
  LAPTOP
  DESKTOP
  PHONE
  TABLET
  PRINTER
  OTHER
}

enum StockMovementType {
  PURCHASE // Stock added via purchase
  SALE // Stock reduced via sale
  TRANSFER_IN // Stock added via transfer from another warehouse
  TRANSFER_OUT // Stock reduced via transfer to another warehouse
  ADJUSTMENT // Manual stock adjustment
  RETURN // Stock returned from customer
  DAMAGE // Stock marked as damaged/lost
}
