"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { z } from "zod";

// Define schema for notification settings
const NotificationSettingsSchema = z.object({
  emailEnabled: z.boolean(),
  emailInfoEnabled: z.boolean(),
  emailWarningEnabled: z.boolean(),
  emailSuccessEnabled: z.boolean(),
  emailErrorEnabled: z.boolean(),
  emailPurchaseEnabled: z.boolean(),
  emailProductAdditionEnabled: z.boolean(),
  emailServiceAdditionEnabled: z.boolean(),
  dailySummary: z.boolean(),
  weeklySummary: z.boolean(),
});

export type NotificationSettingsType = z.infer<
  typeof NotificationSettingsSchema
>;

// Get notification settings for the current user
export async function getNotificationSettings(): Promise<{
  success: boolean;
  data?: NotificationSettingsType;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Get notification settings from database
    let settings = await db.notificationSettings.findUnique({
      where: { userId },
    });

    // If settings don't exist, create default settings
    if (!settings) {
      settings = await db.notificationSettings.create({
        data: {
          userId,
          emailEnabled: true,
          emailInfoEnabled: true,
          emailWarningEnabled: true,
          emailSuccessEnabled: true,
          emailErrorEnabled: true,
          emailPurchaseEnabled: true,
          emailProductAdditionEnabled: true,
          emailServiceAdditionEnabled: true,
          dailySummary: false,
          weeklySummary: true,
        },
      });
    }

    return {
      success: true,
      data: {
        emailEnabled: settings.emailEnabled,
        emailInfoEnabled: settings.emailInfoEnabled,
        emailWarningEnabled: settings.emailWarningEnabled,
        emailSuccessEnabled: settings.emailSuccessEnabled,
        emailErrorEnabled: settings.emailErrorEnabled,
        emailPurchaseEnabled: settings.emailPurchaseEnabled,
        emailProductAdditionEnabled: settings.emailProductAdditionEnabled,
        emailServiceAdditionEnabled: settings.emailServiceAdditionEnabled,
        dailySummary: settings.dailySummary,
        weeklySummary: settings.weeklySummary,
      },
    };
  } catch (error) {
    console.error("Error getting notification settings:", error);
    return {
      success: false,
      error: "Gagal mendapatkan pengaturan notifikasi.",
    };
  }
}

// Update notification settings for the current user
export async function updateNotificationSettings(
  settings: NotificationSettingsType
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Validate settings
    const validatedSettings = NotificationSettingsSchema.safeParse(settings);
    if (!validatedSettings.success) {
      return {
        success: false,
        error: "Pengaturan notifikasi tidak valid.",
      };
    }

    // Update or create notification settings
    await db.notificationSettings.upsert({
      where: { userId },
      update: validatedSettings.data,
      create: {
        userId,
        ...validatedSettings.data,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating notification settings:", error);
    return {
      success: false,
      error: "Gagal memperbarui pengaturan notifikasi.",
    };
  }
}

// Test notification email
export async function sendTestNotificationEmail(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const userId = await getEffectiveUserId();

    if (!userId) {
      return { success: false, error: "Tidak terautentikasi!" };
    }

    // Import dynamically to avoid circular dependencies
    const { sendNotificationEmail } = await import(
      "@/lib/send-notification-email"
    );

    // Send test notification email
    const result = await sendNotificationEmail({
      userId,
      title: "Email Notifikasi Uji Coba",
      message:
        "Ini adalah email uji coba untuk memastikan pengaturan notifikasi email Anda berfungsi dengan baik.",
      type: "info",
    });

    if (!result.success) {
      return {
        success: false,
        error: result.error || "Gagal mengirim email uji coba.",
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending test notification email:", error);
    return {
      success: false,
      error: "Gagal mengirim email uji coba.",
    };
  }
}
