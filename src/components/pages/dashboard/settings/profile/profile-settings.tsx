"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { ProfileSettingsProps, getRoleBadgeColor } from "./types";
import ProfileImage from "./profile-image";
import AccountInfo from "./account-info";
import ReadEditProfileForm from "./read-edit-profile-form";
import EnhancedProfileInfo from "./profile-info";
import { User, UserCog } from "lucide-react";

export default function ProfileSettings({ user }: ProfileSettingsProps) {
  // Form values
  const [name, setName] = useState(user.name || "");
  const [username, setUsername] = useState(user.username || "");
  const [imageUrl, setImageUrl] = useState(user.image || "");
  const [phone, setPhone] = useState(user.phone || "");
  const [bio, setBio] = useState(user.bio || "");
  const [birthday, setBirthday] = useState<Date | undefined>(
    user.birthday ? new Date(user.birthday) : undefined
  );

  const handleReset = () => {
    setName(user.name || "");
    setUsername(user.username || "");
    setImageUrl(user.image || "");
    setPhone(user.phone || "");
    setBio(user.bio || "");
    setBirthday(user.birthday ? new Date(user.birthday) : undefined);
  };

  return (
    <div className="rounded-lg">
      {/* Custom Animation Styles */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>

      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden shadow-sm">
        <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-indigo-50 to-white dark:from-indigo-900/20 dark:to-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Pengaturan Profil
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Kelola informasi pribadi Anda
              </p>
            </div>
            <Badge
              className={`${getRoleBadgeColor(user.role)} px-3 py-1 text-xs font-medium`}
            >
              {user.role}
            </Badge>
          </div>
        </div>

        {/* Combined Profile Content */}
        <div className="p-6 animate-fade-in space-y-8">
          {/* Main Profile Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Profile Image & Account Info */}
            <div className="lg:col-span-1 space-y-6">
              {/* Profile Image */}
              <ProfileImage
                imageUrl={imageUrl}
                setImageUrl={setImageUrl}
                name={name}
                username={username}
              />

              {/* Account Information */}
              <AccountInfo user={user} />
            </div>

            {/* Right Column - Edit Profile Form */}
            <div className="lg:col-span-2">
              <ReadEditProfileForm
                user={user}
                name={name}
                setName={setName}
                username={username}
                setUsername={setUsername}
                imageUrl={imageUrl}
                phone={phone}
                setPhone={setPhone}
                bio={bio}
                setBio={setBio}
                birthday={birthday}
                setBirthday={setBirthday}
                handleReset={handleReset}
              />
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="border-t border-gray-100 dark:border-gray-800 pt-8">
            <EnhancedProfileInfo user={user} />
          </div>
        </div>
      </div>
    </div>
  );
}
