"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { <PERSON>, <PERSON>, <PERSON>, Lapt<PERSON>, Palette } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const themeOptions = [
  {
    value: "light",
    label: "Light",
    icon: Sun,
    description: "Tema terang untuk penggunaan di siang hari",
  },
  {
    value: "dark",
    label: "Dark",
    icon: <PERSON>,
    description: "Tema gelap untuk mengurangi ketegangan mata",
  },
  {
    value: "system",
    label: "System",
    icon: Laptop,
    description: "Mengikuti pengaturan sistem operasi Anda",
  },
];

export default function AppearanceSettings() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  // Ensure component is mounted before accessing theme
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="rounded-lg">
      <Card className="border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden pt-0">
        <CardHeader className="pt-4 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-indigo-50 to-white dark:from-indigo-900/20 dark:to-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-indigo-100 dark:bg-indigo-900/30 p-2 rounded-full">
              <Palette className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div>
              <CardTitle className="text-xl">Pengaturan Tampilan</CardTitle>
              <CardDescription>
                Sesuaikan tampilan aplikasi sesuai preferensi Anda
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6 space-y-8">
          {/* Theme Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-indigo-500" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Tema
              </h3>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Pilih tema yang ingin Anda gunakan untuk aplikasi
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {themeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => {
                    setTheme(option.value);
                    setIsSaved(false);
                  }}
                  className={`relative flex flex-col items-start p-5 rounded-xl border transition-all duration-200 cursor-pointer ${
                    theme === option.value
                      ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 ring-2 ring-indigo-500/20"
                      : "border-gray-200 dark:border-gray-700 hover:border-indigo-200 dark:hover:border-indigo-800 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-750"
                  }`}
                >
                  {theme === option.value && (
                    <div className="absolute top-3 right-3 h-5 w-5 bg-indigo-500 dark:bg-indigo-400 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  )}
                  <div
                    className={`p-2 rounded-full mb-3 ${
                      theme === option.value
                        ? "bg-indigo-100 dark:bg-indigo-900/30"
                        : "bg-gray-100 dark:bg-gray-700"
                    }`}
                  >
                    <option.icon
                      className={`h-5 w-5 ${
                        theme === option.value
                          ? "text-indigo-600 dark:text-indigo-400"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    />
                  </div>
                  <span
                    className={`text-sm font-medium ${
                      theme === option.value
                        ? "text-indigo-700 dark:text-indigo-300"
                        : "text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    {option.label}
                  </span>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {option.description}
                  </p>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t border-gray-100 dark:border-gray-800  dark:bg-gray-800/50 flex justify-between items-center">
          {isSaved && (
            <p className="text-sm text-green-600 dark:text-green-400 flex items-center">
              <Check className="h-4 w-4 mr-1" /> Perubahan telah disimpan
            </p>
          )}
          {!isSaved && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Perubahan akan diterapkan secara otomatis
            </p>
          )}
          <Button onClick={() => setIsSaved(true)} className="cursor-pointer">
            Simpan Perubahan
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
