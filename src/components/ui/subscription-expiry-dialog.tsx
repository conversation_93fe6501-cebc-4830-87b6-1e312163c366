"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface SubscriptionExpiryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const SubscriptionExpiryDialog: React.FC<
  SubscriptionExpiryDialogProps
> = ({ open, onOpenChange }) => {
  const router = useRouter();
  const { data: session } = useSession();

  const handleUpgrade = () => {
    onOpenChange(false);
    router.push("/dashboard/settings/plans");
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
              <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <DialogTitle className="text-lg font-semibold">
              Langganan Berakhir
            </DialogTitle>
          </div>
        </DialogHeader>

        <DialogDescription className="text-base text-gray-600 dark:text-gray-300 py-4">
          Langganan kamu sudah habis, perpanjang untuk bisa membuat transaksi
          baru!
        </DialogDescription>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            className="w-full sm:w-auto"
          >
            Nanti Saja
          </Button>
          <Button
            onClick={handleUpgrade}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
          >
            Perpanjang Sekarang
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Hook to manage subscription expiry dialog state
export const useSubscriptionExpiryDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasShownThisSession, setHasShownThisSession] = useState(false);
  const { data: session } = useSession();

  useEffect(() => {
    const checkSubscriptionExpiry = async () => {
      if (!session?.user?.id || hasShownThisSession) {
        return;
      }

      try {
        const response = await fetch("/api/subscription/expiry");
        if (response.ok) {
          const data = await response.json();
          if (data.expired) {
            setIsOpen(true);
            setHasShownThisSession(true);
          }
        }
      } catch (error) {
        console.error("Error checking subscription expiry:", error);
      }
    };

    // Check subscription expiry when session is available
    if (session?.user?.id) {
      checkSubscriptionExpiry();
    }
  }, [session?.user?.id, hasShownThisSession]);

  return {
    isOpen,
    setIsOpen,
    hasShownThisSession,
  };
};
